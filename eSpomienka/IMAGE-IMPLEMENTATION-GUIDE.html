<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eSpomienka.sk - Image Implementation Guide</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .example { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .code { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; }
        h2 { color: #2c3e50; border-bottom: 2px solid #daa520; padding-bottom: 10px; }
        .note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🖼️ eSpomienka.sk - Image Implementation Guide</h1>
    
    <h2>1. Hero Section Background</h2>
    <div class="example">
        <h3>Responsive Hero Background with WebP Support</h3>
        <div class="code">
&lt;section class="hero" style="position: relative; min-height: 100vh;"&gt;
    &lt;picture&gt;
        &lt;source media="(min-width: 1024px)" 
                srcset="assets/images/hero/hero-background-desktop.webp" 
                type="image/webp"&gt;
        &lt;source media="(min-width: 1024px)" 
                srcset="assets/images/hero/hero-background-desktop.jpg"&gt;
        &lt;source media="(min-width: 768px)" 
                srcset="assets/images/hero/hero-background-tablet.webp" 
                type="image/webp"&gt;
        &lt;source media="(min-width: 768px)" 
                srcset="assets/images/hero/hero-background-tablet.jpg"&gt;
        &lt;source srcset="assets/images/hero/hero-background-mobile.webp" 
                type="image/webp"&gt;
        &lt;img src="assets/images/hero/hero-background-mobile.jpg" 
             alt="Pokojný les s ranným svetlom - eSpomienka.sk" 
             style="width: 100%; height: 100vh; object-fit: cover; position: absolute; top: 0; left: 0; z-index: -1;"&gt;
    &lt;/picture&gt;
    
    &lt;div class="hero-content" style="position: relative; z-index: 2; padding: 100px 20px; text-align: center; color: white;"&gt;
        &lt;h1&gt;Digitálne spomienky na večnosť&lt;/h1&gt;
        &lt;p&gt;Vytvárame memorial videá, webstránky a QR kódy pre uctenie pamiatky&lt;/p&gt;
    &lt;/div&gt;
&lt;/section&gt;
        </div>
    </div>

    <h2>2. Services Section Images</h2>
    <div class="example">
        <h3>Memorial Website Service</h3>
        <div class="code">
&lt;div class="service-card"&gt;
    &lt;picture&gt;
        &lt;source media="(min-width: 1024px)" 
                srcset="assets/images/services/service-website-desktop.webp" 
                type="image/webp"&gt;
        &lt;source media="(min-width: 1024px)" 
                srcset="assets/images/services/service-website-desktop.jpg"&gt;
        &lt;source media="(min-width: 768px)" 
                srcset="assets/images/services/service-website-tablet.webp" 
                type="image/webp"&gt;
        &lt;source media="(min-width: 768px)" 
                srcset="assets/images/services/service-website-tablet.jpg"&gt;
        &lt;source srcset="assets/images/services/service-website-mobile.webp" 
                type="image/webp"&gt;
        &lt;img src="assets/images/services/service-website-mobile.jpg" 
             alt="Memorial webstránka na laptope - eSpomienka.sk" 
             loading="lazy"
             style="width: 100%; height: auto; border-radius: 8px;"&gt;
    &lt;/picture&gt;
    &lt;h3&gt;Memorial Webstránky&lt;/h3&gt;
    &lt;p&gt;Elegantné webové stránky na uctenie pamiatky&lt;/p&gt;
&lt;/div&gt;
        </div>
    </div>

    <h2>3. Process Steps Icons</h2>
    <div class="example">
        <h3>SVG Icons with PNG Fallback</h3>
        <div class="code">
&lt;div class="process-steps"&gt;
    &lt;div class="step"&gt;
        &lt;img src="assets/images/process/step-1-contact.svg" 
             alt="Kontakt - Krok 1" 
             onerror="this.src='assets/images/process/step-1-contact.png'"
             style="width: 64px; height: 64px;"&gt;
        &lt;h4&gt;1. Kontakt&lt;/h4&gt;
        &lt;p&gt;Bezplatná konzultácia&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class="step"&gt;
        &lt;img src="assets/images/process/step-2-materials.svg" 
             alt="Materiály - Krok 2" 
             onerror="this.src='assets/images/process/step-2-materials.png'"
             style="width: 64px; height: 64px;"&gt;
        &lt;h4&gt;2. Materiály&lt;/h4&gt;
        &lt;p&gt;Zdieľanie fotografií a spomienok&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class="step"&gt;
        &lt;img src="assets/images/process/step-3-creation.svg" 
             alt="Tvorba - Krok 3" 
             onerror="this.src='assets/images/process/step-3-creation.png'"
             style="width: 64px; height: 64px;"&gt;
        &lt;h4&gt;3. Tvorba&lt;/h4&gt;
        &lt;p&gt;Profesionálne spracovanie&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class="step"&gt;
        &lt;img src="assets/images/process/step-4-delivery.svg" 
             alt="Dodanie - Krok 4" 
             onerror="this.src='assets/images/process/step-4-delivery.png'"
             style="width: 64px; height: 64px;"&gt;
        &lt;h4&gt;4. Dodanie&lt;/h4&gt;
        &lt;p&gt;Finálny produkt&lt;/p&gt;
    &lt;/div&gt;
&lt;/div&gt;
        </div>
    </div>

    <h2>4. Credibility Section</h2>
    <div class="example">
        <h3>Trust Badge and Support Image</h3>
        <div class="code">
&lt;div class="credibility-section"&gt;
    &lt;div class="trust-badge"&gt;
        &lt;picture&gt;
            &lt;source srcset="assets/images/credibility/trust-guarantee-standard.webp" 
                    type="image/webp"&gt;
            &lt;img src="assets/images/credibility/trust-guarantee-standard.jpg" 
                 alt="10-ročná záruka - eSpomienka.sk" 
                 loading="lazy"
                 style="width: 100px; height: 100px;"&gt;
        &lt;/picture&gt;
        &lt;h4&gt;10-ročná záruka&lt;/h4&gt;
    &lt;/div&gt;
    
    &lt;div class="support-image"&gt;
        &lt;picture&gt;
            &lt;source media="(min-width: 1024px)" 
                    srcset="assets/images/credibility/support-care-desktop.webp" 
                    type="image/webp"&gt;
            &lt;source media="(min-width: 1024px)" 
                    srcset="assets/images/credibility/support-care-desktop.jpg"&gt;
            &lt;source media="(min-width: 768px)" 
                    srcset="assets/images/credibility/support-care-tablet.webp" 
                    type="image/webp"&gt;
            &lt;source media="(min-width: 768px)" 
                    srcset="assets/images/credibility/support-care-tablet.jpg"&gt;
            &lt;source srcset="assets/images/credibility/support-care-mobile.webp" 
                    type="image/webp"&gt;
            &lt;img src="assets/images/credibility/support-care-mobile.jpg" 
                 alt="Starostlivá podpora - eSpomienka.sk" 
                 loading="lazy"
                 style="width: 100%; height: auto; border-radius: 8px;"&gt;
        &lt;/picture&gt;
    &lt;/div&gt;
&lt;/div&gt;
        </div>
    </div>

    <h2>5. CSS for Optimal Performance</h2>
    <div class="example">
        <h3>Performance-Optimized CSS</h3>
        <div class="code">
/* Lazy loading support */
img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
    opacity: 1;
}

/* Responsive images */
img {
    max-width: 100%;
    height: auto;
}

/* Hero section optimization */
.hero {
    position: relative;
    overflow: hidden;
}

.hero picture img {
    will-change: transform;
    transform: translateZ(0);
}

/* Service cards hover effects */
.service-card {
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-8px);
}

/* Process steps responsive grid */
.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

@media (max-width: 768px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}
        </div>
    </div>

    <div class="note">
        <strong>💡 Performance Tips:</strong>
        <ul>
            <li>Use <code>loading="lazy"</code> for images below the fold</li>
            <li>WebP format provides 30% smaller file sizes</li>
            <li>Always include alt text for accessibility</li>
            <li>Use <code>onerror</code> fallback for SVG icons</li>
            <li>Implement proper caching headers for images</li>
        </ul>
    </div>

    <div class="note">
        <strong>📱 Mobile Optimization:</strong>
        <ul>
            <li>Mobile images are optimized for portrait viewing</li>
            <li>Total mobile payload under 500KB</li>
            <li>Progressive loading with smallest images first</li>
            <li>Touch-friendly hover effects</li>
        </ul>
    </div>

</body>
</html>
