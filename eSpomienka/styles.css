/* Custom CSS for eSpomienka - Modern Light Memorial Website */

/* Import Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&family=Dancing+Script:wght@400;500;600;700&family=Nunito:wght@300;400;500;600&display=swap');

/* Global Performance & UX Optimizations */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-display: swap;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* CSS Variables for Modern Light Color Palette - Online-Tribute Inspired */
:root {
    /* Primary Backgrounds - Clean & Professional */
    --background-primary: #F8F9FA;
    --background-secondary: #FFFFFF;
    --background-tertiary: #F1F3F5;

    /* Text Colors - High Contrast & Readable */
    --text-primary: #212529;
    --text-secondary: #6C757D;
    --text-muted: #ADB5BD;

    /* Accent Colors - Professional Blue */
    --accent-primary: #0d6efd;
    --accent-secondary: #0b5ed7;
    --accent-light: #cfe2ff;

    /* Borders & Shadows - Subtle & Clean */
    --border-color: #DEE2E6;
    --border-light: #E9ECEF;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

    /* Interactive States */
    --hover-bg: #F8F9FA;
    --focus-ring: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);

    /* Gradients - Subtle & Modern */
    --gradient-primary: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);

    /* Legacy compatibility */
    --bg-primary: var(--background-primary);
    --bg-secondary: var(--background-secondary);
    --gold: #D4A574;
    --cream: #FAFAFA;
    --dark-gray: var(--text-primary);
    --white: var(--background-secondary);
}

/* Modern Typography System - Online-Tribute Inspired */

/* Base Typography */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    font-weight: 400;
    letter-spacing: 0.01em;
}

/* Heading Styles - Clean & Professional */
.text-h1, h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 3.5rem;
    font-weight: 500;
    line-height: 1.1;
    color: var(--text-primary);
    letter-spacing: -0.02em;
    margin-bottom: 1.5rem;
}

.text-h2, h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 500;
    line-height: 1.2;
    color: var(--text-primary);
    letter-spacing: -0.01em;
    margin-bottom: 1.25rem;
}

.text-h3, h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.75rem;
    font-weight: 500;
    line-height: 1.3;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

/* Body Text Styles */
.text-body-lg, .lead {
    font-family: 'Nunito', sans-serif;
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    font-weight: 400;
}

.text-body, p {
    font-family: 'Nunito', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* Special Typography */
.text-handwritten-accent {
    font-family: 'Dancing Script', cursive;
    color: var(--accent-primary);
    font-weight: 600;
    font-size: 1.5rem;
}

.text-handwritten-quote {
    font-family: 'Dancing Script', cursive;
    font-size: 1.75rem;
    font-style: italic;
    color: var(--accent-primary);
    line-height: 1.4;
    text-align: center;
    margin: 2rem 0;
}

/* Responsive Typography */
@media (max-width: 768px) {
    .text-h1 {
        font-size: 2.5rem;
    }

    .text-h2 {
        font-size: 2rem;
    }

    .text-body-lg {
        font-size: 1.125rem;
    }

    .text-handwritten-quote {
        font-size: 1.25rem;
    }
}

/* ===== BLOG OPTIMIZATION STYLES ===== */

/* Blog CTA Banner */
.blog-cta-banner {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(44, 62, 80, 0.15);
}

.cta-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.cta-text h3 {
    color: #DAA520;
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    font-family: 'Playfair Display', serif;
}

.cta-text p {
    margin: 0;
    opacity: 0.9;
}

.btn-blog-cta {
    background: #DAA520;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    white-space: nowrap;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-blog-cta:hover {
    background: #B8860B;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
}

@media (max-width: 768px) {
    .cta-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .blog-cta-banner {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .cta-text h3 {
        font-size: 1.2rem;
    }
}

/* Related Articles Widget */
.related-articles {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    margin: 3rem 0;
    border: 1px solid #e9ecef;
}

.related-articles h3 {
    color: var(--primary);
    margin-bottom: 1.5rem;
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.related-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.related-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--gold);
}

.related-item a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.related-item h4 {
    color: var(--gold);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-family: 'Playfair Display', serif;
    line-height: 1.3;
}

.related-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

/* Newsletter Signup */
.newsletter-signup {
    background: linear-gradient(135deg, #DAA520 0%, #FFD700 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(218, 165, 32, 0.2);
}

.newsletter-content h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.newsletter-content p {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
}

.newsletter-form input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.newsletter-form button {
    background: var(--primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    white-space: nowrap;
    font-weight: 600;
    transition: all 0.3s ease;
}

.newsletter-form button:hover {
    background: #1a252f;
    transform: translateY(-1px);
}

.newsletter-note {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.5rem;
}

@media (max-width: 768px) {
    .newsletter-form {
        flex-direction: column;
        gap: 1rem;
    }

    .newsletter-signup {
        padding: 1.5rem;
    }
}

/* Blog Navigation Enhancement */
.blog-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

.nav-home {
    color: #666;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-home:hover {
    color: var(--gold);
}

.btn-nav-cta {
    background: var(--gold);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-nav-cta:hover {
    background: #B8860B;
    transform: translateY(-1px);
}

/* Breadcrumbs */
.breadcrumbs {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 2rem;
    padding: 0.5rem 0;
}

.breadcrumbs a {
    color: var(--gold);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumbs a:hover {
    color: #B8860B;
}

.breadcrumbs span {
    color: #999;
}

/* Blog Card Enhancements */
.blog-card {
    transition: all 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
}

.blog-card .read-more {
    color: var(--gold);
    font-weight: 600;
    transition: color 0.3s ease;
}

.blog-card .read-more:hover {
    color: #B8860B;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .blog-nav {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .related-articles {
        padding: 1.5rem;
        margin: 2rem 0;
    }
}

/* Custom Font Classes */
.font-playfair {
    font-family: 'Playfair Display', serif;
}

.font-inter {
    font-family: 'Inter', sans-serif;
}

/* Custom Color Classes */
.text-primary { color: var(--primary); }
.text-gold { color: var(--gold); }
.text-accent { color: var(--accent); }
.text-dark-gray { color: var(--dark-gray); }
.bg-primary { background-color: var(--primary); }
.bg-gold { background-color: var(--gold); }
.bg-accent { background-color: var(--accent); }
.bg-cream { background-color: var(--cream); }
.bg-dark-gray { background-color: var(--dark-gray); }

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Body Styling */
body {
    line-height: 1.6;
    color: var(--dark-gray);
    overflow-x: hidden;
}

/* SAFETY: Prevent any global overlays */
body::before,
html::before {
    display: none !important;
}

/* Ensure no fixed overlays cover the entire page */
*[style*="position: fixed"]::before,
*[style*="position: fixed"]::after {
    position: absolute !important;
}

/* Container */
.container {
    max-width: 1200px;
}

/* Header Styling */
header {
    background-color: rgba(44, 62, 80, 0.95) !important;
    backdrop-filter: blur(10px);
    z-index: 50;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Mobile header - fixed position */
@media (max-width: 768px) {
    header {
        position: fixed !important;
        top: 0;
        left: 0;
        right: 0;
        z-index: 50;
    }

    /* Add padding to body to compensate for fixed header */
    body {
        padding-top: 0;
    }

    /* Hero section padding for mobile */
    #domov {
        padding-top: 80px;
    }
}

/* Logo styling for better contrast */
header .font-playfair {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Navigation Links */
.nav-link {
    position: relative;
    color: #F5F5F5 !important;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-link:hover {
    color: var(--gold) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--gold);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Modern Button Styles */
.btn-primary-modern {
    background: var(--gradient-gold);
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-md);
    cursor: pointer;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-primary));
}

.btn-secondary-modern {
    background: rgba(255,255,255,0.9);
    color: var(--gold-primary);
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: 2px solid var(--gold-primary);
    box-shadow: var(--shadow-sm);
    cursor: pointer;
}

.btn-secondary-modern:hover {
    background: var(--gold-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Legacy button styles for compatibility */
.btn-primary {
    background: var(--gradient-gold);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: rgba(255,255,255,0.9);
    color: var(--gold-primary);
    padding: 1rem 2rem;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: 2px solid var(--gold-primary);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--gold-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Hero Section - FIXED Z-INDEX SOLUTION */
.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 2s ease-in-out;
    animation: kenBurns 15s infinite;
}

.hero-slide.active {
    opacity: 1;
}

/* REMOVED ::before overlay to avoid conflict with HTML overlay */
/* The HTML already has: <div class="absolute inset-0 bg-gradient-to-r from-dark-gray/70 to-dark-gray/50 z-10"></div> */

/* Hero text container with proper z-index */
.hero-text-enhanced {
    position: relative;
    z-index: 25 !important; /* Higher than overlay (z-10) but lower than z-30 */
    text-shadow:
        0 4px 8px rgba(0, 0, 0, 0.8),
        0 2px 4px rgba(0, 0, 0, 0.6);
}

/* Ensure hero section has proper positioning context */
#domov {
    position: relative; /* Required for absolute positioning of children */
}

/* Keep Tailwind z-index values but ensure text is visible */
#domov .z-20 {
    z-index: 20 !important; /* Text content - use original Tailwind value */
    position: relative !important;
}

#domov .z-30 {
    z-index: 30 !important; /* Scroll indicator - use original Tailwind value */
}

/* Zlaté akcenty s extra glow */
.hero-gold-text {
    color: #FFD700 !important;
    text-shadow:
        0 4px 8px rgba(0, 0, 0, 0.9),
        0 2px 4px rgba(0, 0, 0, 0.7),
        0 0 15px rgba(255, 215, 0, 0.4);
    font-weight: 700;
}

@keyframes kenBurns {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0,-30px,0);
    }
    70% {
        transform: translate3d(0,-15px,0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

/* Scroll Indicator */
.scroll-indicator {
    width: 30px;
    height: 50px;
    border: 2px solid var(--white);
    border-radius: 25px;
    position: relative;
    animation: bounce 2s infinite;
}

.scroll-dot {
    width: 6px;
    height: 6px;
    background-color: var(--white);
    border-radius: 50%;
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    animation: scrollDot 2s infinite;
}

@keyframes scrollDot {
    0% { top: 8px; opacity: 1; }
    50% { top: 30px; opacity: 0.5; }
    100% { top: 8px; opacity: 1; }
}

/* Service Cards */
.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--light-gray);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--gold);
}

.service-card.featured {
    border: 2px solid var(--gold);
    position: relative;
}

.service-card.featured::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--gold), var(--accent));
    border-radius: 1rem;
    z-index: -1;
    opacity: 0.1;
}

.service-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--gold), #B8860B);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: var(--white);
}

/* Pricing Cards */
.pricing-card {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--light-gray);
    position: relative;
    height: 100%;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.pricing-card.featured {
    border: 2px solid var(--gold);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

/* Form Styling */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--light-gray);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background-color: var(--white);
}

.form-input:focus {
    outline: none;
    border-color: var(--gold);
    box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.1);
}

.contact-form {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

/* Video Player Styling */
video {
    border-radius: 0.5rem;
}

video::-webkit-media-controls-panel {
    background-color: rgba(44, 62, 80, 0.8);
}

video::-webkit-media-controls-play-button {
    background-color: var(--gold);
    border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-slide {
        background-position: center center;
    }

    /* Hero text container responzívne úpravy */
    .hero-text-container {
        padding: 2rem 1.5rem;
        margin: 0 1rem;
        border-radius: 8px;
    }

    .hero-title {
        font-size: 2.5rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.125rem !important;
        line-height: 1.4;
    }

    .service-card,
    .pricing-card {
        margin-bottom: 2rem;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* AOS Fallback - Ensure content is visible even if AOS fails to load */
[data-aos] {
    opacity: 1 !important;
    transform: none !important;
    transition: all 0.3s ease;
}

/* AOS Animation States */
.aos-init {
    opacity: 1 !important;
    transform: none !important;
}

.aos-animate {
    opacity: 1 !important;
    transform: none !important;
}

/* Ensure AOS elements are visible by default */
[data-aos="fade-up"],
[data-aos="fade-down"],
[data-aos="fade-left"],
[data-aos="fade-right"],
[data-aos="zoom-in"],
[data-aos="zoom-out"] {
    opacity: 1 !important;
    transform: none !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--cream);
}

::-webkit-scrollbar-thumb {
    background: var(--gold);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #B8860B;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(218, 165, 32, 0.3);
    border-radius: 50%;
    border-top-color: var(--gold);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Blog Section Styles */
.posts-grid {
    display: grid;
    gap: 2rem;
}

.blog-card {
    background: #FFFFFF !important;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
}

/* Blog Category Tags in Hero */
.blog-category-tag {
    background: var(--gold) !important;
    color: #FFFFFF !important;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(218, 165, 32, 0.3);
    display: inline-block;
}

.blog-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--gold) !important;
    color: #FFFFFF !important;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(218, 165, 32, 0.3);
}

.blog-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #666666;
}

.blog-meta .date {
    font-weight: 500;
    color: #666666 !important;
}

.blog-meta .read-time {
    color: var(--gold) !important;
    font-weight: 500;
}

.blog-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.25rem;
    font-weight: 600 !important;
    color: #2C3E50 !important;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.blog-excerpt {
    color: #333333 !important;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.read-more {
    color: var(--gold);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: color 0.3s ease;
    margin-top: auto;
}

.read-more:hover {
    color: #B8860B;
}

.read-more::after {
    content: '→';
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.read-more:hover::after {
    transform: translateX(3px);
}

/* Newsletter Section */
.blog-newsletter {
    background: linear-gradient(135deg, var(--primary) 0%, #34495E 100%);
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-input {
    font-size: 1rem;
    background: var(--white);
}

.newsletter-button {
    background: var(--gold);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.newsletter-button:hover {
    background: #B8860B;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);
}

/* Responsive Design for Blog */
@media (max-width: 768px) {
    .posts-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .newsletter-form .flex {
        flex-direction: column;
    }

    .newsletter-form .flex > * {
        width: 100%;
    }

    .blog-title {
        font-size: 1.125rem;
    }

    .blog-image {
        height: 180px;
    }
}

/* ===== COOKIES BANNER STYLES ===== */
.cookie-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(10px);
    color: white;
    padding: 1.5rem;
    z-index: 9999;
    transform: translateY(100%);
    transition: transform 0.3s ease-in-out;
    border-top: 3px solid var(--gold);
}

.cookie-banner.show {
    transform: translateY(0);
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.cookie-text h4 {
    margin: 0 0 0.5rem 0;
    color: var(--gold);
    font-size: 1.2rem;
    font-family: 'Playfair Display', serif;
    font-weight: 600;
}

.cookie-text p {
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.4;
    color: #F5F5F5;
}

.privacy-link {
    color: var(--gold);
    text-decoration: underline;
    transition: color 0.3s ease;
}

.privacy-link:hover {
    color: #B8860B;
}

.cookie-buttons {
    display: flex;
    gap: 1rem;
    flex-shrink: 0;
}

.btn-accept-all {
    background: var(--gold);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    font-family: 'Inter', sans-serif;
}

.btn-accept-all:hover {
    background: #B8860B;
}

.btn-accept-necessary,
.btn-cookie-settings {
    background: transparent;
    color: white;
    border: 2px solid var(--gold);
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
}

.btn-accept-necessary:hover,
.btn-cookie-settings:hover {
    background: var(--gold);
    color: white;
}

@media (max-width: 768px) {
    .cookie-content {
        flex-direction: column;
        text-align: center;
    }

    .cookie-buttons {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cookie-buttons button {
        flex: 1;
        min-width: 120px;
    }
}

/* ===== PRIVACY POLICY STYLES ===== */
.privacy-policy {
    background: var(--cream);
    min-height: 100vh;
}

.policy-content {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    line-height: 1.7;
}

.policy-content h2 {
    color: var(--dark-gray);
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    margin: 2rem 0 1rem 0;
    border-bottom: 2px solid var(--gold);
    padding-bottom: 0.5rem;
}

.policy-content h3 {
    color: var(--dark-gray);
    font-size: 1.2rem;
    margin: 1.5rem 0 0.5rem 0;
    font-weight: 600;
}

.policy-content h4 {
    color: var(--gold);
    font-size: 1.1rem;
    margin: 1rem 0 0.5rem 0;
    font-weight: 600;
}

.policy-content p {
    margin-bottom: 1rem;
    color: var(--dark-gray);
}

.policy-content ul {
    margin: 1rem 0;
    padding-left: 1rem;
}

.policy-content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.cookie-settings-link {
    background: var(--gold);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s ease;
    display: inline-block;
    text-decoration: none;
}

.cookie-settings-link:hover {
    background: #B8860B;
    color: white;
}

/* Privacy Policy responsive design */
@media (max-width: 768px) {
    .privacy-policy h1 {
        font-size: 2rem;
    }

    .policy-content {
        margin: 1rem;
        padding: 1.5rem;
    }

    .policy-content h2 {
        font-size: 1.3rem;
    }
}

/* ===== MOBILE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
    /* Mobile header improvements */
    .mobile-menu {
        background: rgba(44, 62, 80, 0.98);
        backdrop-filter: blur(10px);
    }

    /* Mobile hero adjustments */
    .hero-text-enhanced h1 {
        font-size: 2.5rem !important;
        line-height: 1.2;
    }

    .hero-text-enhanced p {
        font-size: 1.1rem !important;
    }

    /* Mobile button spacing */
    .hero-text-enhanced .space-y-4 > * + * {
        margin-top: 1rem;
    }

    /* Mobile section padding */
    section {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    /* Mobile contact section */
    #kontakt .grid {
        gap: 2rem;
    }

    /* Mobile pricing cards */
    .bg-cream {
        margin-bottom: 1rem;
    }

    /* Mobile navigation improvements */
    .nav-link {
        font-size: 1.1rem;
        padding: 0.75rem 0;
    }

    /* Mobile form improvements */
    .contact-form input,
    .contact-form textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Clickable Package Cards */
.clickable-package {
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none !important;
    color: inherit !important;
}

.clickable-package:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(218, 165, 32, 0.25);
    border: 2px solid #DAA520;
}

.clickable-package.featured:hover {
    transform: scale(1.05) translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(218, 165, 32, 0.3);
}

/* Details Button */
.btn-details {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #DAA520;
    color: #DAA520;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-details:hover {
    background: #DAA520;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(218, 165, 32, 0.3);
}

/* Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.breadcrumb a {
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #DAA520;
}



/* Comparison Table */
.comparison-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    width: 100%;
}

.comparison-table th {
    background: #DAA520;
    color: white;
    padding: 16px;
    font-weight: 600;
    text-align: center;
}

.comparison-table td {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    text-align: center;
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

/* Sticky CTA */
.sticky-cta {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 50;
    animation: fadeInUp 0.5s ease-out;
    box-shadow: 0 10px 25px rgba(218, 165, 32, 0.3);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Package Hero Sections */
.package-hero {
    background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
    position: relative;
    overflow: hidden;
}

.package-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(218, 165, 32, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.package-hero .container {
    position: relative;
    z-index: 2;
}

/* Process Steps */
.process-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.process-step:last-child {
    margin-bottom: 0;
}

.process-number {
    width: 48px;
    height: 48px;
    background: #DAA520;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
    flex-shrink: 0;
    margin-right: 1rem;
}

.process-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 0.5rem;
}

.process-content p {
    color: #6B7280;
    line-height: 1.6;
}

/* FAQ Styling */
.faq-item {
    background: #F9FAFB;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.faq-item:hover {
    background: #F3F4F6;
    transform: translateY(-2px);
}

.faq-question {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 0.75rem;
}

.faq-answer {
    color: #6B7280;
    line-height: 1.6;
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
    .clickable-package:hover {
        transform: translateY(-4px);
    }

    .clickable-package.featured:hover {
        transform: scale(1.02) translateY(-4px);
    }

    .sticky-cta {
        bottom: 16px;
        right: 16px;
        font-size: 14px;
        padding: 12px 16px;
    }

    .comparison-table {
        font-size: 14px;
    }

    .comparison-table th,
    .comparison-table td {
        padding: 12px 8px;
    }

    .process-number {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Enhanced Hero Section */
.hero-enhanced {
    background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 100%);
}

/* Trust Badges */
.trust-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

/* FAQ Toggle Functionality */
.faq-toggle.active svg {
    transform: rotate(180deg);
}

.faq-content {
    transition: all 0.3s ease;
}

/* Optimized Spacing */
section {
    padding-top: 5rem;
    padding-bottom: 5rem;
}

/* Enhanced Contrast */
body {
    line-height: 1.6;
    color: #2C3E50;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem;
    }

    .trust-badges {
        gap: 1rem;
    }

    section {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .faq-toggle {
        font-size: 0.95rem;
    }
}

/* Animation Optimizations */
* {
    transition-duration: 300ms !important;
}

/* Touch-friendly buttons */
@media (max-width: 768px) {
    .btn-primary,
    .btn-secondary,
    .faq-toggle {
        min-height: 44px;
        min-width: 44px;
    }
}

/* ===== MODERN LIGHT HERO SECTION ===== */
.hero-optimized {
    min-height: 100vh;
    display: flex;
    align-items: center;
    color: var(--text-primary);
    text-align: center;
    position: relative;
    background: var(--gradient-hero);
    background-image:
        url('assets/images/hero/hero-background-desktop.webp'),
        url('assets/images/hero/hero-accent-standard.webp');
    background-size: cover, contain;
    background-position: center center, top right;
    background-repeat: no-repeat, no-repeat;
    background-blend-mode: soft-light, normal;
}

/* Responsive hero backgrounds */
@media (max-width: 1023px) {
    .hero-optimized {
        background-image:
            url('assets/images/hero/hero-background-tablet.webp'),
            url('assets/images/hero/hero-accent-standard.webp');
    }
}

@media (max-width: 767px) {
    .hero-optimized {
        background-image: url('assets/images/hero/hero-background-mobile.webp');
        background-size: cover;
        background-position: center center;
    }
}

/* Hero background with new peaceful forest image */
.hero-optimized {
    background-image: url('assets/images/hero/hero-background-mobile.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

@media (min-width: 768px) {
    .hero-optimized {
        background-image: url('assets/images/hero/hero-background-tablet.jpg');
    }
}

@media (min-width: 1200px) {
    .hero-optimized {
        background-image: url('assets/images/hero/hero-background-desktop.jpg');
    }
}

/* Ensure service images display properly */
.feature-image {
    display: block;
    overflow: hidden;
    border-radius: 8px;
}

.feature-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.feature-image:hover img {
    transform: scale(1.05);
}

/* Process step icons */
.step-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-icon img {
    width: 40px;
    height: 40px;
    display: block;
}

/* Light overlay for text readability */
.hero-optimized::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.85) 0%,
        rgba(255, 255, 255, 0.75) 50%,
        rgba(241, 245, 249, 0.85) 100%);
    pointer-events: none;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-content h1 {
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.hero-gold-text {
    color: var(--gold-primary);
    font-family: 'Dancing Script', cursive;
    font-weight: 600;
}

.hero-handwritten-quote {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    color: var(--gold-primary);
    font-style: italic;
    margin: 1rem 0 2rem 0;
    opacity: 0.9;
}

.trust-badges {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.trust-badges span {
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 2rem;
    max-width: 600px;
    margin: 2rem auto 0;
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow-md);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--gold-primary);
    display: block;
    font-family: 'Poppins', sans-serif;
}

.stat-item span:last-child {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Mobile responsive for hero */
@media (max-width: 768px) {
    .hero-optimized {
        padding-top: 80px;
        min-height: 90vh;
    }

    .hero-content h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-handwritten-quote {
        font-size: 1.25rem;
    }

    .trust-badges {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .trust-badges span {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 1.5rem;
        margin: 1.5rem auto 0;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-content {
        padding: 1rem;
    }
}

/* ===== MODERN SERVICES SECTION ===== */
.service-package-card {
    background: var(--bg-primary);
    border: 2px solid var(--gold-primary);
    border-radius: 24px;
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.service-package-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-gold);
}

.package-header {
    margin-bottom: 3rem;
}

.package-badge {
    display: inline-block;
    background: var(--gold-light);
    color: var(--gold-dark);
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.package-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
}

.package-title {
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--gold-primary);
    margin-bottom: 1rem;
}

.package-price {
    margin-bottom: 1rem;
}

.price-main {
    font-size: 3rem;
    font-weight: 700;
    font-family: 'Dancing Script', cursive;
}

.price-vat {
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-left: 0.5rem;
}

.package-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.service-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.service-feature-card {
    background: var(--bg-secondary);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.service-feature-card:hover {
    transform: translateY(-8px);
    border-color: var(--gold-primary);
    box-shadow: var(--shadow-lg);
}

.feature-image {
    width: 100%;
    height: 200px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.feature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-feature-card:hover .feature-image img {
    transform: scale(1.05);
}

.service-feature-card h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.service-feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.additional-features {
    margin: 3rem 0;
    padding: 2rem;
    background: var(--bg-soft);
    border-radius: 16px;
}

.features-row {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.features-row .feature-item {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1.1rem;
}

.guarantee-section {
    background: var(--gold-light);
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
}

.guarantee-content {
    color: var(--gold-dark);
    font-weight: 600;
    text-align: center;
    line-height: 1.6;
}

.package-cta {
    margin-top: 3rem;
}

.package-cta p {
    margin-top: 1rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Mobile responsive for services */
@media (max-width: 768px) {
    .service-package-card {
        padding: 2rem 1.5rem;
    }

    .package-title {
        font-size: 2rem;
    }

    .price-main {
        font-size: 2.5rem;
    }

    .service-features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .service-feature-card {
        padding: 1.5rem;
    }

    .feature-image {
        height: 150px;
    }

    .features-row {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .additional-features {
        padding: 1.5rem;
        margin: 2rem 0;
    }
}

/* ===== MODERN PROCESS SECTION ===== */
.process-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.process-step {
    text-align: center;
    padding: 2rem;
    background: var(--bg-primary);
    border-radius: 20px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
    position: relative;
}

.process-step:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--gold-primary);
}

.step-icon {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.step-icon img {
    width: 40px;
    height: 40px;
    filter: brightness(0) invert(1);
}

.step-number {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 28px;
    height: 28px;
    background: var(--gold-dark);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: var(--shadow-sm);
}

.process-step h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.process-step p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
}

/* Mobile responsive for process */
@media (max-width: 768px) {
    .process-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .process-step {
        padding: 1.5rem;
    }

    .step-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1.5rem;
    }

    .step-icon img {
        width: 30px;
        height: 30px;
    }

    .step-number {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
        top: -6px;
        right: -6px;
    }

    .process-step h3 {
        font-size: 1.2rem;
    }

    .process-step p {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .process-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* ===== MODERN CREDIBILITY SECTION ===== */
.credibility-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.credibility-card {
    background: var(--bg-secondary);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.credibility-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--gold-primary);
}

.credibility-icon {
    width: 70px;
    height: 70px;
    background: var(--gold-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--gold-primary);
    transition: all 0.3s ease;
}

.credibility-card:hover .credibility-icon {
    background: var(--gradient-gold);
    color: white;
    transform: scale(1.1);
}

.credibility-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.credibility-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Trust Section */
.trust-section {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
    background-image: url('assets/images/credibility/support-care-desktop.webp');
    background-size: cover;
    background-position: center;
    background-blend-mode: soft-light;
    border-radius: 24px;
    padding: 4rem 2rem;
    position: relative;
    overflow: hidden;
}

.trust-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.9) 0%,
        rgba(241, 245, 249, 0.85) 100%);
    z-index: 1;
}

.trust-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.trust-quote {
    margin-bottom: 3rem;
    font-size: 2rem;
}

.trust-badges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.trust-badge:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.trust-badge-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.trust-badge h4 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.trust-badge p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Mobile responsive for credibility */
@media (max-width: 768px) {
    .credibility-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .credibility-card {
        padding: 2rem 1.5rem;
    }

    .credibility-icon {
        width: 60px;
        height: 60px;
    }

    .trust-section {
        padding: 3rem 1.5rem;
    }

    .trust-quote {
        font-size: 1.5rem;
        margin-bottom: 2rem;
    }

    .trust-badges-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .trust-badge {
        padding: 1.25rem;
    }
}

@media (max-width: 480px) {
    .credibility-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== MODERN CONTACT SECTION ===== */
.contact-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
    background-image: url('assets/images/contact/contact-background-desktop.jpg');
    background-size: cover;
    background-position: center;
    background-blend-mode: soft-light;
    position: relative;
}

.contact-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.9) 0%,
        rgba(241, 245, 249, 0.85) 100%);
    z-index: 1;
}

.contact-section .container {
    position: relative;
    z-index: 2;
}

/* Responsive contact backgrounds */
@media (max-width: 1023px) {
    .contact-section {
        background-image: url('assets/images/contact/contact-background-tablet.jpg');
    }
}

@media (max-width: 767px) {
    .contact-section {
        background-image: url('assets/images/contact/contact-background-mobile.jpg');
        padding: 3rem 0;
    }
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Contact Info Styles */
.contact-info {
    background: rgba(255, 255, 255, 0.95);
    padding: 3rem;
    border-radius: 24px;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

.contact-info-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.contact-items {
    margin-bottom: 3rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.contact-item h4 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-link {
    color: var(--gold-primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: var(--gold-dark);
}

.contact-note {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.contact-benefits h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.benefits-list {
    list-style: none;
    padding: 0;
}

.benefits-list li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.benefits-list svg {
    color: var(--gold-primary);
    flex-shrink: 0;
}

/* Contact Form Styles */
.contact-form-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.modern-contact-form {
    padding: 3rem;
}

.form-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid var(--border-light);
    border-radius: 12px;
    font-family: 'Nunito', sans-serif;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--gold-primary);
    box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--text-muted);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    cursor: pointer;
    position: relative;
}

.form-checkbox input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-radius: 4px;
    background: var(--bg-primary);
    position: relative;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.form-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-gold);
    border-color: var(--gold-primary);
}

.form-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-text {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
}

.checkbox-text a {
    color: var(--gold-primary);
    text-decoration: none;
}

.checkbox-text a:hover {
    text-decoration: underline;
}

.form-submit-btn {
    width: 100%;
    background: var(--gradient-gold);
    color: white;
    padding: 1.25rem 2rem;
    border: none;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
}

.form-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-primary));
}

.form-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.response-note,
.consultation-note {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0.5rem 0;
}

.response-note {
    color: var(--gold-primary);
    font-weight: 600;
}

/* Mobile responsive for contact */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info,
    .modern-contact-form {
        padding: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .contact-item {
        padding: 1.25rem;
    }

    .contact-icon {
        width: 45px;
        height: 45px;
    }
}

/* ===== MODERN HEADER STYLES ===== */
.modern-header {
    position: static;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    z-index: 50;
    border-bottom: 1px solid var(--border-light);
}

.header-logo-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--gold-primary);
}

.nav-link-modern {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link-modern:hover {
    color: var(--gold-primary);
    background: var(--gold-light);
}

.header-phone {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    color: var(--gold-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.header-phone:hover {
    color: var(--gold-dark);
    transform: translateY(-1px);
}

/* Mobile menu styles */
.mobile-menu-btn {
    padding: 0.5rem;
    color: var(--text-primary);
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    color: var(--gold-primary);
}

.mobile-menu {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-light);
    box-shadow: var(--shadow-md);
}

.mobile-menu-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-nav-link {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover {
    color: var(--gold-primary);
    background: var(--gold-light);
}

.mobile-phone-link {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--gold-primary);
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background: var(--gold-light);
    text-align: center;
    margin-top: 0.5rem;
}

/* Mobile header styles */
@media (max-width: 768px) {
    .modern-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
    }

    .header-logo-text {
        font-size: 1.5rem;
    }

    /* Adjust hero section for fixed header */
    .hero-optimized {
        padding-top: 80px;
    }
}

/* ===== MODERN FOOTER STYLES ===== */
.modern-footer {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
    padding: 4rem 0 2rem;
    border-top: 1px solid var(--border-light);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.footer-logo-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gold-primary);
}

.footer-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social-link {
    width: 45px;
    height: 45px;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-social-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.footer-heading {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.footer-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-list li {
    margin-bottom: 0.75rem;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.footer-link:hover {
    color: var(--gold-primary);
}

.footer-contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-contact-link {
    color: var(--gold-primary);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.footer-contact-link:hover {
    color: var(--gold-dark);
}

.footer-hours {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.footer-bottom {
    border-top: 1px solid var(--border-light);
    padding-top: 2rem;
    text-align: center;
}

.footer-copyright {
    margin-bottom: 1.5rem;
}

.footer-copyright p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.footer-tagline {
    font-size: 1.1rem;
    margin-top: 0.5rem;
}

.footer-legal {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.footer-legal-link {
    color: var(--text-muted);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    background: none;
    border: none;
    cursor: pointer;
}

.footer-legal-link:hover {
    color: var(--gold-primary);
}

.footer-info {
    color: var(--text-muted);
    font-size: 0.85rem;
    margin-top: 1rem;
}

/* Mobile responsive for footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-brand {
        max-width: none;
    }

    .footer-social {
        justify-content: center;
    }

    .footer-legal {
        flex-direction: column;
        gap: 1rem;
    }
}

/* ===== OPTIMIZED PRICING CARD ===== */
.pricing-card-new {
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #DAA520;
    border-radius: 24px;
    padding: 3rem;
    text-align: center;
}

.pricing-header {
    margin-bottom: 3rem;
}

.pricing-badge {
    background: #DAA520;
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    display: inline-block;
    margin-bottom: 2rem;
}

.pricing-price {
    margin: 2rem 0;
}

.price-main {
    font-size: 4rem;
    font-weight: bold;
    color: #DAA520;
}

.price-vat {
    font-size: 1.2rem;
    color: #666;
    margin-left: 0.5rem;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 3rem 0;
    text-align: left;
}

.feature-column {
    space-y: 1rem;
}

.feature-item {
    padding: 0.75rem 0;
    font-size: 1.1rem;
}

.pricing-guarantee {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    margin: 2rem 0;
    font-size: 0.95rem;
}

.pricing-cta {
    margin-top: 2rem;
}

.btn-primary-large {
    background: linear-gradient(135deg, #DAA520 0%, #FFD700 100%);
    color: white;
    padding: 1rem 3rem;
    border-radius: 50px;
    font-size: 1.2rem;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
    }
    .pricing-card-new {
        padding: 2rem 1.5rem;
    }
}

/* ===== OPTIMIZED FAQ SECTION ===== */
.faq-container {
    max-width: 4xl;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.faq-toggle {
    width: 100%;
    padding: 1.5rem;
    background: none;
    border: none;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
}

.faq-toggle:hover {
    background-color: #f9fafb;
}

.faq-arrow {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.faq-toggle.active .faq-arrow {
    transform: rotate(180deg);
}

.faq-content {
    padding: 0 1.5rem 1.5rem;
    color: #666;
    display: none;
}

.faq-content.active {
    display: block;
}

/* ===== OPTIMIZED CONTACT FORM ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #DAA520;
}

.urgency-checkbox,
.consent-checkbox {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    cursor: pointer;
}

.urgency-checkbox input,
.consent-checkbox input {
    margin-right: 0.75rem;
    margin-top: 0.25rem;
    margin-bottom: 0;
}

.btn-contact-submit {
    width: 100%;
    background: linear-gradient(135deg, #DAA520 0%, #FFD700 100%);
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.btn-contact-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
}

.response-time {
    text-align: center;
    font-weight: 600;
    color: #DAA520;
    margin: 0;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}
